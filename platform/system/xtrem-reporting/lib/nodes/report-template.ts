import {
    BinaryStream,
    Collection,
    Context,
    DataInputError,
    Logger,
    Node,
    NodeStatus,
    Reference,
    TextStream,
    TextStreamDataType,
    asyncArray,
    decimal,
    decorators,
} from '@sage/xtrem-core';
import { withRethrow, type Dict } from '@sage/xtrem-shared';
import * as xtremSystem from '@sage/xtrem-system';
import { difference, keyBy, uniq } from 'lodash';
import { WIZARD_PREVIEW_TEMPLATE_NAME } from '../functions/generic-utils';
import graphqlFromHtmlGenerator from '../functions/graphql-from-html-generator';
import { defaultHtmlFooter, defaultHtmlHeader } from '../functions/html-document-builder';
import { getDocumentEditorStyleSheet } from '../functions/style-utils';
import { extractTranslatableContent, restoreTranslationContent } from '../functions/translation-utils';
import * as xtremReporting from '../index';

const logger = Logger.getLogger(__filename, 'reporting-report-template-node');

const fileNameRegex = /(?:\.([^.]+))?$/;
const mimeTypeRegex = /^[-\w.]+\/[-\w.]+$/;

const templateTextStreamType = new TextStreamDataType({
    maxLength: 1000000,
    allowedContentTypes: ['text/html', 'text/plain'],
});

const styleSheetTextStreamType = new TextStreamDataType({
    maxLength: 1000000,
    allowedContentTypes: ['text/css', 'text/plain'],
});

const attachmentTemplateType = new TextStreamDataType({
    maxLength: 1000000,
    allowedContentTypes: ['text/html', 'text/plain', 'application/xml', 'text/xml', 'application/json'],
});

function getHtmlString(html: TextStream | undefined) {
    if (html == null) {
        return '';
    }
    return withRethrow(
        async () => (await html.sanitized()).toString(),
        err => new DataInputError(err.message, err),
    );
}

@decorators.node<ReportTemplate>({
    package: 'xtrem-reporting',
    storage: 'sql',
    indexes: [{ orderBy: { name: 1 }, isUnique: true, isNaturalKey: true }],
    isSetupNode: true,
    isPublished: true,
    canCreate: true,
    canRead: true,
    canSearch: true,
    canDelete: true,
    canUpdate: true,
    canDuplicate: true,
    async controlEnd(cx) {
        if (await this.isFactory) {
            cx.error.add(
                this.$.context.localize(
                    '@sage/xtrem-reporting/nodes__report-template__cannot-modify-factory-template',
                    'Cannot modify a factory template.',
                ),
            );
        }
    },
    async prepare() {
        const externalHeaderHtmlTemplate = await this.externalHeaderHtmlTemplate;
        const externalFooterHtmlTemplate = await this.externalFooterHtmlTemplate;
        const externalHtmlTemplate = await this.externalHtmlTemplate;
        const isExpertDocument = await this.isExpertDocument;

        if (!isExpertDocument) {
            const generatedQuery = graphqlFromHtmlGenerator(
                externalHtmlTemplate.toString(),
                externalHeaderHtmlTemplate?.toString(),
                externalFooterHtmlTemplate?.toString(),
                await this.baseLocale,
            );
            const generatedStyleSheet = await getDocumentEditorStyleSheet();
            await this.$.set({
                query: TextStream.fromString(generatedQuery, 'application/graphql'),
                styleSheet: TextStream.fromString(generatedStyleSheet, 'text/css'),
            });
        }

        if (this.$.status === 'modified') {
            const oldInstance = await this.$.old;
            const oldHeaderHtmlTemplate = await oldInstance.headerHtmlTemplate;
            const oldFooterHtmlTemplate = await oldInstance.footerHtmlTemplate;
            const oldHtmlTemplate = await oldInstance.htmlTemplate;

            if (
                oldHeaderHtmlTemplate.compareTo(externalHeaderHtmlTemplate) === 0 &&
                oldFooterHtmlTemplate.compareTo(externalFooterHtmlTemplate) === 0 &&
                oldHtmlTemplate.compareTo(externalHtmlTemplate) === 0
            ) {
                // If the template content doesn't change, then we don't need to extract the translatable strings;
                return;
            }
        }
        const { content: bodyTemplate, extractedContent: bodyLiterals } = extractTranslatableContent(
            externalHtmlTemplate.toString(),
        );
        await this.$.set({ htmlTemplate: TextStream.fromString(bodyTemplate) });

        const { content: headerTemplate, extractedContent: headerLiterals } = extractTranslatableContent(
            externalHeaderHtmlTemplate.toString(),
        );
        await this.$.set({ headerHtmlTemplate: TextStream.fromString(headerTemplate) });

        const { content: footerTemplate, extractedContent: footerLiterals } = extractTranslatableContent(
            externalFooterHtmlTemplate.toString(),
        );
        await this.$.set({ footerHtmlTemplate: TextStream.fromString(footerTemplate) });

        const extractedContent = { ...bodyLiterals, ...headerLiterals, ...footerLiterals };
        const currentKeys = Object.keys(extractedContent);
        const previousKeys = uniq(await this.translatableTexts.map(i => i.hash).toArray());
        const entriesToRemove = difference(previousKeys, currentKeys);
        // retrieve all entries need to be removed and we need to reverse the array to make sure that the original reference will be deleted last
        const indexesToRemove = (
            await asyncArray(await this.translatableTexts.toArray()).reduce(async (currentValue, item, index) => {
                if (entriesToRemove.indexOf(await item.hash) !== -1) {
                    return [...currentValue, index];
                }
                return currentValue;
            }, [] as number[])
        ).reverse();

        await asyncArray(indexesToRemove).forEach(async index => {
            const node = await this.translatableTexts.elementAt(index);
            // delete the node in db
            await node.$.delete();
            // then delete the node in the collection
            await this.translatableTexts.delete(index, 1);
        });
        await asyncArray(difference(currentKeys, previousKeys)).forEach(async (e, i) => {
            const _sortValue = i * 1000;
            await this.translatableTexts.append({
                locale: await this.baseLocale,
                hash: e,
                isBaseLocale: true,
                text: extractedContent[e]!,
                _sortValue,
            });

            const originalText = (await this.translatableTexts.elementAt((await this.translatableTexts.length) - 1))
                ._id;

            await asyncArray(xtremReporting.enums.ReportLocaleDataType.values).forEach(async (locale, j) => {
                if (locale !== (await this.baseLocale)) {
                    await this.translatableTexts.append({
                        locale,
                        isBaseLocale: false,
                        text: '',
                        hash: e,
                        originalText,
                        _sortValue: _sortValue + j,
                    });
                }
            });
        });
    },
    async saveBegin() {
        if (this.$.status === NodeStatus.added || (await this.name) !== (await (await this.$.old).name)) {
            // eslint-disable-next-line @sage/redos/no-vulnerable
            const camelCasedName = (await this.name).replace(/\W+(.)/g, (_match, chr) => {
                return chr.toUpperCase();
            });

            await logger.infoAsync(async () => `New Template Name: ${await this.name}  camelCased: ${camelCasedName}`);

            // validate if name is camelCased
            if ((await this.name) !== camelCasedName) {
                throw this.$.context.businessRuleError({
                    key: '@sage/xtrem-reporting/nodes__report-template__name-camelcased',
                    message: 'The template name is not camelCased.',
                });
            }
        }
    },
})
export class ReportTemplate extends Node {
    private async getCurrentStringsAsDictionary(): Promise<Dict<string>> {
        return asyncArray(await this.translatableTexts.filter(t => t.isBaseLocale).toArray()).reduce(
            async (prevValue, currentValue) => {
                return { ...prevValue, [await currentValue.hash]: (await currentValue.text)! };
            },
            {} as Dict<string>,
        );
    }

    @decorators.stringProperty<ReportTemplate, 'name'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        isFrozen() {
            return this.isFactory;
        },
        dataType: () => xtremSystem.dataTypes.name,
        lookupAccess: true,
        duplicateRequiresPrompt: true,
        async duplicatedValue() {
            const templateNameWithoutNumbers = (await this.name).replace(/[0-9]/g, '');
            const templatesWithSameBaseName = (
                await this.$.context
                    .query(ReportTemplate, {
                        filter: { name: { _regex: `${templateNameWithoutNumbers}[0-9]+` } },
                        orderBy: { name: +1 },
                        last: 1,
                    })
                    .toArray()
            )[0];

            // if no match use default
            const matches = templatesWithSameBaseName ? (await templatesWithSameBaseName.name).match(/[0-9]+/g) : [0];
            // increments the last number matched in the name
            const nextNumber = matches ? Number(matches[matches.length - 1]) + 1 : '1';
            return `${templateNameWithoutNumbers}${nextNumber}`;
        },
    })
    readonly name: Promise<string>;

    @decorators.textStreamProperty<ReportTemplate, 'htmlTemplate'>({
        isStored: true,
        isPublished: false,
        dataType: () => templateTextStreamType,
    })
    readonly htmlTemplate: Promise<TextStream>;

    @decorators.textStreamProperty<ReportTemplate, 'attachmentTemplate'>({
        isStored: true,
        isPublished: true,
        dataType: () => attachmentTemplateType,
    })
    readonly attachmentTemplate: Promise<TextStream>;

    @decorators.stringProperty<ReportTemplate, 'attachmentName'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: false, // when set to true, couldn't save or duplicated a report template because 'max allowed chars' error.
        dataType: () => xtremSystem.dataTypes.name,
        dependsOn: ['attachmentTemplate'],
        async control(cx) {
            if (
                (await this.attachmentTemplate).toString() &&
                (!(await this.attachmentName) || !(await this.attachmentName).match(fileNameRegex)?.[0])
            ) {
                cx.error.add(
                    this.$.context.localize(
                        '@sage/xtrem-reporting/nodes__report-template__wrong-attachment-extension',
                        'Attachment file name should have an extension.',
                    ),
                );
            }
        },
    })
    readonly attachmentName: Promise<string>;

    @decorators.stringProperty<ReportTemplate, 'attachmentMimeType'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: false, // when set to true, couldn't save or duplicated a report template because 'max allowed chars' error.
        dataType: () => xtremSystem.dataTypes.name,
        dependsOn: ['attachmentTemplate'],
        async control(cx) {
            if (
                (await this.attachmentTemplate).toString() &&
                (!(await this.attachmentMimeType) || !(await this.attachmentMimeType).match(mimeTypeRegex)?.[0])
            ) {
                cx.error.add(
                    this.$.context.localize(
                        '@sage/xtrem-reporting/nodes__report-template__wrong-attachment-mimetype',
                        'The attachment mimetype is not valid.',
                    ),
                );
            }
        },
    })
    readonly attachmentMimeType: Promise<string>;

    @decorators.textStreamProperty<ReportTemplate, 'externalHtmlTemplate'>({
        isPublished: true,
        dependsOn: ['htmlTemplate', 'translatableTexts'],
        dataType: () => templateTextStreamType,
        async computeValue() {
            const strings = await this.getCurrentStringsAsDictionary();
            return TextStream.fromString(restoreTranslationContent((await this.htmlTemplate).toString(), strings));
        },
        async setValue(value: TextStream) {
            await this.$.set({ htmlTemplate: value });
        },
    })
    readonly externalHtmlTemplate: Promise<TextStream>;

    @decorators.textStreamProperty<ReportTemplate, 'headerHtmlTemplate'>({
        isStored: true,
        isPublished: false,
        dataType: () => templateTextStreamType,
    })
    readonly headerHtmlTemplate: Promise<TextStream>;

    @decorators.textStreamProperty<ReportTemplate, 'externalHeaderHtmlTemplate'>({
        isPublished: true,
        dependsOn: ['headerHtmlTemplate', 'translatableTexts'],
        dataType: () => templateTextStreamType,
        async computeValue() {
            const strings = await this.getCurrentStringsAsDictionary();
            return TextStream.fromString(
                restoreTranslationContent((await this.headerHtmlTemplate).toString(), strings),
            );
        },
        async setValue(value: TextStream) {
            await this.$.set({ headerHtmlTemplate: value });
        },
    })
    readonly externalHeaderHtmlTemplate: Promise<TextStream>;

    @decorators.textStreamProperty<ReportTemplate, 'footerHtmlTemplate'>({
        isStored: true,
        isPublished: false,
        dataType: () => templateTextStreamType,
    })
    readonly footerHtmlTemplate: Promise<TextStream>;

    @decorators.textStreamProperty<ReportTemplate, 'externalFooterHtmlTemplate'>({
        isPublished: true,
        dependsOn: ['footerHtmlTemplate', 'translatableTexts'],
        dataType: () => templateTextStreamType,
        async computeValue() {
            const strings = await this.getCurrentStringsAsDictionary();
            return TextStream.fromString(
                restoreTranslationContent((await this.footerHtmlTemplate).toString(), strings),
            );
        },
        async setValue(value: TextStream) {
            await this.$.set({ footerHtmlTemplate: value });
        },
    })
    readonly externalFooterHtmlTemplate: Promise<TextStream>;

    @decorators.textStreamProperty<ReportTemplate, 'styleSheet'>({
        isStored: true,
        isPublished: true,
        dataType: () => styleSheetTextStreamType,
    })
    readonly styleSheet: Promise<TextStream>;

    @decorators.textStreamProperty<ReportTemplate, 'query'>({
        isStored: true,
        isPublished: true,
        dataType: () => templateTextStreamType,
    })
    readonly query: Promise<TextStream>;

    @decorators.textStreamProperty<ReportTemplate, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => templateTextStreamType,
    })
    readonly code: Promise<TextStream>;

    @decorators.referenceProperty<ReportTemplate, 'report'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        ignoreInToposort: true,
        node: () => xtremReporting.nodes.Report,
        lookupAccess: true,
        duplicateRequiresPrompt: true,
    })
    readonly report: Reference<xtremReporting.nodes.Report | null>;

    @decorators.referenceProperty<ReportTemplate, 'reportWizard'>({
        isPublished: true,
        isNullable: true,
        ignoreInToposort: true,
        node: () => xtremReporting.nodes.ReportWizard,
        lookupAccess: true,
        isVital: true,
        reverseReference: 'reportTemplate',
    })
    readonly reportWizard: Reference<xtremReporting.nodes.ReportWizard | null>;

    @decorators.booleanProperty<ReportTemplate, 'isFactory'>({
        isStored: true,
        isPublished: true,
        duplicatedValue: false,
        lookupAccess: true,
    })
    readonly isFactory: Promise<boolean>;

    @decorators.booleanProperty<ReportTemplate, 'isExpertDocument'>({
        isStored: true,
        isPublished: true,
        isOwnedByCustomer: true,
        lookupAccess: true,
        async control(ctx) {
            if (this.$.status === 'modified') {
                const oldInstance = await this.$.old;
                const newValue = await this.isExpertDocument;
                const oldValue = await oldInstance.isExpertDocument;
                if (oldValue === true && newValue === false) {
                    ctx.error.addLocalized(
                        '@sage/xtrem-reporting/removing-expert-flag',
                        'You cannot convert an advanced document to one that is not.',
                    );
                }
            }
        },
        defaultValue() {
            return false;
        },
    })
    readonly isExpertDocument: Promise<boolean>;

    @decorators.enumProperty<ReportTemplate, 'reportType'>({
        isPublished: true,
        dataType: () => xtremReporting.enums.ReportTypeDataType,
        async getValue() {
            return (await (await this.report)?.reportType) ?? 'printedDocument';
        },
        lookupAccess: true,
    })
    readonly reportType: Promise<xtremReporting.enums.ReportType>;

    @decorators.enumProperty<ReportTemplate, 'templateType'>({
        isPublished: true,
        dataType: () => xtremReporting.enums.TemplateTypeDataType,
        async getValue() {
            if ((await this.reportWizard) != null) {
                return 'list';
            }
            if (await this.isExpertDocument) {
                return 'advanced';
            }
            return 'form';
        },
        lookupAccess: true,
    })
    readonly templateType: Promise<xtremReporting.enums.TemplateType>;

    @decorators.query<typeof ReportTemplate, 'getSchema'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'string',
        },
    })
    static getSchema(context: Context): any {
        return JSON.stringify(
            context.executeGraphql(
                `query IntrospectionQuery {
                __schema {
                  types {
                    kind
                    name
                    description
                    fields {
                      name
                      type {
                        kind
                        name
                        ofType{
                          kind
                          name
                        }
                      }
                    }
                  }
                }
              }`,
            ),
        );
    }

    @decorators.collectionProperty<ReportTemplate, 'translatableTexts'>({
        node: () => xtremReporting.nodes.ReportTranslatableText,
        isPublished: true,
        isVital: true,
        reverseReference: 'reportTemplate',
        async duplicatedValue() {
            const sourceId = await this.$.sourceId;
            if (!sourceId) {
                return [];
            }
            const sourceNode = await this.$.context.read(xtremReporting.nodes.ReportTemplate, sourceId);
            const translatableTexts = await sourceNode.translatableTexts.toArray();
            return Promise.all(translatableTexts.map(async (translatableText: xtremReporting.nodes.ReportTranslatableText) => {
                return {
                    ...(await translatableText.$.flattenSlices()),
                    _id: undefined,
                };
            }));
        },
    })
    readonly translatableTexts: Collection<xtremReporting.nodes.ReportTranslatableText>;

    @decorators.enumProperty<ReportTemplate, 'baseLocale'>({
        isPublished: true,
        isStored: true,
        defaultValue() {
            return 'en_US';
        },
        dataType: () => xtremReporting.enums.ReportLocaleDataType,
        duplicateRequiresPrompt: true,
        lookupAccess: true,
    })
    readonly baseLocale: Promise<xtremReporting.enums.ReportLocale>;

    @decorators.enumProperty<ReportTemplate, 'defaultPaperFormat'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremReporting.enums.ReportPaperFormatDataType,
        duplicateRequiresPrompt: true,
        lookupAccess: true,
    })
    readonly defaultPaperFormat: Promise<xtremReporting.enums.ReportPaperFormat>;

    @decorators.enumProperty<ReportTemplate, 'defaultPageOrientation'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremReporting.enums.ReportPageOrientationDataType,
        defaultValue() {
            return 'portrait';
        },
        lookupAccess: true,
    })
    readonly defaultPageOrientation: Promise<xtremReporting.enums.ReportPageOrientation>;

    @decorators.decimalProperty<ReportTemplate, 'defaultLeftMargin'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremSystem.dataTypes.decimal,
        defaultValue() {
            return 2;
        },
    })
    readonly defaultLeftMargin: Promise<decimal>;

    @decorators.decimalProperty<ReportTemplate, 'defaultRightMargin'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremSystem.dataTypes.decimal,
        defaultValue() {
            return 2;
        },
    })
    readonly defaultRightMargin: Promise<decimal>;

    @decorators.decimalProperty<ReportTemplate, 'defaultTopMargin'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremSystem.dataTypes.decimal,
        defaultValue() {
            return 2;
        },
    })
    readonly defaultTopMargin: Promise<decimal>;

    @decorators.decimalProperty<ReportTemplate, 'defaultBottomMargin'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremSystem.dataTypes.decimal,
        defaultValue() {
            return 2;
        },
    })
    readonly defaultBottomMargin: Promise<decimal>;

    @decorators.booleanProperty<ReportTemplate, 'isDefaultHeaderFooter'>({
        isStored: true,
        isPublished: true,
        duplicatedValue: false,
        lookupAccess: true,
        defaultValue: false,
    })
    readonly isDefaultHeaderFooter: Promise<boolean>;

    @decorators.mutation<typeof ReportTemplate, 'generateReportSample'>({
        isPublished: true,
        parameters: [
            {
                name: 'reportTemplate',
                type: 'object',
                isMandatory: true,
                properties: {
                    _id: {
                        name: '_id',
                        type: 'string',
                        isMandatory: false,
                    },
                    isExpertDocument: {
                        name: 'isExpertDocument',
                        type: 'boolean',
                        isMandatory: false,
                    },
                    baseLocale: {
                        name: 'baseLocale',
                        type: 'string',
                        isMandatory: false,
                    },
                    styleSheet: {
                        name: 'styleSheet',
                        type: 'textStream',
                        isMandatory: true,
                    },
                    externalHtmlTemplate: {
                        name: 'externalHtmlTemplate',
                        type: 'textStream',
                        isMandatory: true,
                    },
                    externalHeaderHtmlTemplate: {
                        name: 'externalHeaderHtmlTemplate',
                        type: 'textStream',
                        isMandatory: false,
                    },
                    externalFooterHtmlTemplate: {
                        name: 'externalFooterHtmlTemplate',
                        type: 'textStream',
                        isMandatory: false,
                    },
                    attachmentTemplate: {
                        name: 'attachmentTemplate',
                        type: 'textStream',
                        isMandatory: false,
                    },
                    attachmentName: {
                        name: 'attachmentName',
                        type: 'string',
                        isMandatory: false,
                    },
                    attachmentMimeType: {
                        name: 'attachmentMimeType',
                        type: 'string',
                        isMandatory: false,
                    },
                    query: {
                        name: 'query',
                        type: 'textStream',
                        isMandatory: true,
                    },
                    code: {
                        name: 'code',
                        type: 'textStream',
                        isMandatory: true,
                    },
                    translatableTexts: {
                        name: 'translatableTexts',
                        type: 'array',
                        item: {
                            type: 'object',
                            properties: {
                                hash: {
                                    name: 'hash',
                                    type: 'string',
                                    isMandatory: false,
                                },
                                locale: {
                                    name: 'locale',
                                    type: 'enum',
                                    isMandatory: false,
                                    dataType: () => xtremReporting.enums.ReportLocaleDataType,
                                },
                                text: {
                                    name: 'text',
                                    type: 'string',
                                    isMandatory: false,
                                },
                            },
                        },
                    },
                },
            },
            {
                type: 'object',
                name: 'reportSettings',
                properties: {
                    variables: {
                        name: 'variables',
                        type: 'string',
                        isMandatory: true,
                    },
                    locale: {
                        name: 'locale',
                        type: 'string',
                        isMandatory: false,
                    },
                    paperFormat: {
                        name: 'paperFormat',
                        type: 'string',
                        isMandatory: false,
                    },
                    pageOrientation: {
                        name: 'pageOrientation',
                        type: 'string',
                        isMandatory: false,
                    },
                    leftMarginCm: {
                        name: 'leftMarginCm',
                        type: 'decimal',
                        isMandatory: false,
                    },
                    rightMarginCm: {
                        name: 'rightMarginCm',
                        type: 'decimal',
                        isMandatory: false,
                    },
                    topMarginCm: {
                        name: 'topMarginCm',
                        type: 'decimal',
                        isMandatory: false,
                    },
                    bottomMarginCm: {
                        name: 'bottomMarginCm',
                        type: 'decimal',
                        isMandatory: false,
                    },
                    watermark: {
                        name: 'watermark',
                        type: 'string',
                        isMandatory: false,
                    },
                },
            },
        ],
        return: {
            type: 'binaryStream',
        },
    })
    static async generateReportSample(
        context: Context,
        reportTemplate: ReportTemplatePreview,
        reportSettings: xtremReporting.functions.IncomingReportGenerationSettings,
    ): Promise<BinaryStream> {
        const savedReportTemplate = reportTemplate._id
            ? await context.tryRead(xtremReporting.nodes.ReportTemplate, { _id: reportTemplate._id })
            : null;

        // Manually merge translations in the editor page to existing translations to be able to generate a live preview.
        const savedTranslations = savedReportTemplate ? await savedReportTemplate.translatableTexts.toArray() : [];
        const savedTranslationsObject = keyBy(savedTranslations, async t => `${await t.hash}-${await t.locale}`);
        const incomingTranslationsObject = keyBy(reportTemplate.translatableTexts || [], t => `${t.hash}-${t.locale}`);
        const listOfTranslationKeys = [
            ...Object.keys(savedTranslationsObject),
            ...Object.keys(incomingTranslationsObject),
        ].map(k => incomingTranslationsObject[k] || savedTranslationsObject[k]);

        const variables = reportSettings.variables ? JSON.parse(reportSettings.variables) : {};
        const localeToUse = xtremReporting.functions.getLocaleToUse(context, reportSettings.locale);
        const paperFormatToUse = xtremReporting.functions.getPaperFormatToUse(localeToUse, reportSettings.paperFormat);

        const topMarginCm = reportSettings.topMarginCm || (await savedReportTemplate?.defaultTopMargin) || 2;
        const bottomMarginCm = reportSettings.bottomMarginCm || (await savedReportTemplate?.defaultBottomMargin) || 2;
        const leftMarginCm = reportSettings.leftMarginCm || (await savedReportTemplate?.defaultLeftMargin) || 2;
        const rightMarginCm = reportSettings.rightMarginCm || (await savedReportTemplate?.defaultRightMargin) || 2;

        const { content: bodyHtmlTemplate, extractedContent: bodyStrings } =
            xtremReporting.functions.extractTranslatableContent(
                await getHtmlString(reportTemplate.externalHtmlTemplate),
            );

        const { content: headerHtmlTemplate, extractedContent: headerStrings } =
            xtremReporting.functions.extractTranslatableContent(
                await getHtmlString(reportTemplate.externalHeaderHtmlTemplate),
            );
        const { content: footerHtmlTemplate, extractedContent: footerStrings } =
            xtremReporting.functions.extractTranslatableContent(
                await getHtmlString(reportTemplate.externalFooterHtmlTemplate),
            );

        const strings = {
            ...bodyStrings,
            ...headerStrings,
            ...footerStrings,
        };

        const translationsToUse = xtremReporting.functions.createDictionaryForRendering(
            localeToUse,
            reportTemplate.baseLocale || 'en_US',
            listOfTranslationKeys,
            strings,
        );

        const reportName = reportTemplate.reportName;
        const query = reportTemplate.isExpertDocument
            ? reportTemplate.query
            : TextStream.fromString(
                  graphqlFromHtmlGenerator(bodyHtmlTemplate, headerHtmlTemplate, footerHtmlTemplate, localeToUse),
              );

        const reportObject = await xtremReporting.functions.getReportObjectFromName(context, reportName, []);

        const styleSheet = reportTemplate.isExpertDocument
            ? reportTemplate.styleSheet
            : TextStream.fromString(await getDocumentEditorStyleSheet());

        const code = reportTemplate.isExpertDocument ? reportTemplate.code : TextStream.fromString('');

        const { populatedBodyContent, populatedFooterContent, populatedHeaderContent, populatedAttachmentContent } =
            await xtremReporting.functions.generateReportFromTemplateContents({
                context,
                reportName,
                baseLocale: reportTemplate.baseLocale || 'en_US',
                bodyHtmlTemplate,
                headerHtmlTemplate,
                footerHtmlTemplate,
                query,
                code,
                styleSheet,
                variables,
                locale: localeToUse,
                paperFormat: paperFormatToUse,
                translations: translationsToUse,
                attachmentTemplate: reportTemplate.attachmentTemplate,
                isExpertDocument: reportTemplate.isExpertDocument,
                isSample: true,
                watermark: reportSettings.watermark,
            });

        return BinaryStream.fromBuffer(
            await xtremReporting.functions.generatePdfData({
                context,
                reportName,
                reportObject,
                populatedBodyContent,
                populatedHeaderContent,
                populatedFooterContent,
                paperFormat: paperFormatToUse,
                pageOrientation: reportSettings.pageOrientation || 'portrait',
                outputPath: '',
                populatedAttachment: populatedAttachmentContent,
                attachmentName: reportTemplate.attachmentName,
                attachmentMimeType: reportTemplate.attachmentMimeType,
                leftMarginCm,
                rightMarginCm,
                topMarginCm,
                bottomMarginCm,
                isSample: true,
            }),
        );
    }

    @decorators.query<typeof ReportTemplate, 'exportTranslations'>({
        isPublished: true,
        parameters: [
            {
                name: 'sourceLocale',
                type: 'string',
                isMandatory: false,
            },
            {
                name: 'targetLocale',
                type: 'string',
                isMandatory: false,
            },
            {
                name: 'reportTemplates',
                type: 'array',
                isMandatory: false,
                item: {
                    type: 'reference',
                    node: () => xtremReporting.nodes.ReportTemplate,
                },
            },
        ],
        return: {
            type: 'binaryStream',
        },
    })
    static async exportTranslations(
        context: Context,
        sourceLocale: xtremReporting.enums.ReportLocale,
        targetLocale: xtremReporting.enums.ReportLocale,
        reportTemplates?: xtremReporting.nodes.ReportTemplate[],
    ): Promise<BinaryStream> {
        const translatableContent = await xtremReporting.functions.exportTranslations(
            context,
            sourceLocale,
            targetLocale,
            reportTemplates,
        );
        if (Object.keys(translatableContent).length === 0) {
            throw new Error('No translatable content was found.');
        }

        return BinaryStream.fromBuffer(
            Buffer.from(
                JSON.stringify(
                    await xtremReporting.functions.exportTranslations(
                        context,
                        sourceLocale,
                        targetLocale,
                        reportTemplates,
                    ),
                ),
                'utf-8',
            ),
        );
    }

    @decorators.mutation<typeof ReportTemplate, 'importTranslations'>({
        isPublished: true,
        parameters: [
            {
                name: 'input',
                type: 'object',
                isMandatory: true,
                properties: {
                    translations: {
                        name: 'translations',
                        type: 'binaryStream',
                    },
                },
            },
        ],
        return: {
            type: 'array',
            item: {
                type: 'boolean',
            },
        },
    })
    static async importTranslations(context: Context, input: { translations: BinaryStream }): Promise<boolean[]> {
        await xtremReporting.functions.importTranslations(
            context,
            JSON.parse(input.translations.value.toString('utf8')),
        );
        return [true];
    }

    @decorators.mutation<typeof ReportTemplate, 'generateWizardPreview'>({
        isPublished: true,
        parameters: [
            {
                name: 'template',
                type: 'string',
                isMandatory: true,
            },
            {
                type: 'object',
                name: 'reportSettings',
                properties: {
                    variables: {
                        name: 'variables',
                        type: 'string',
                        isMandatory: true,
                    },
                    locale: {
                        name: 'locale',
                        type: 'string',
                        isMandatory: false,
                    },
                    paperFormat: {
                        name: 'paperFormat',
                        type: 'string',
                        isMandatory: false,
                    },
                    pageOrientation: {
                        name: 'pageOrientation',
                        type: 'string',
                        isMandatory: false,
                    },
                    leftMarginCm: {
                        name: 'leftMarginCm',
                        type: 'decimal',
                        isMandatory: false,
                    },
                    rightMarginCm: {
                        name: 'rightMarginCm',
                        type: 'decimal',
                        isMandatory: false,
                    },
                    topMarginCm: {
                        name: 'topMarginCm',
                        type: 'decimal',
                        isMandatory: false,
                    },
                    bottomMarginCm: {
                        name: 'bottomMarginCm',
                        type: 'decimal',
                        isMandatory: false,
                    },
                    isDefaultHeaderFooter: {
                        name: 'isDefaultHeaderFooter',
                        type: 'boolean',
                        isMandatory: false,
                    },
                    reportName: {
                        name: 'reportName',
                        type: 'string',
                        isMandatory: false,
                    },
                },
            },
        ],
        return: {
            type: 'binaryStream',
        },
    })
    static async generateWizardPreview(
        context: Context,
        template: string,
        reportSettings: xtremReporting.functions.IncomingReportGenerationSettings,
    ): Promise<BinaryStream> {
        const localeToUse = xtremReporting.functions.getLocaleToUse(context, reportSettings.locale);
        const paperFormatToUse = xtremReporting.functions.getPaperFormatToUse(localeToUse, reportSettings.paperFormat);
        const parameters = reportSettings.variables ? JSON.parse(reportSettings.variables) : {};
        const { populatedBodyContent, populatedFooterContent, populatedHeaderContent } =
            await xtremReporting.functions.generateReportFromTemplateContents({
                context,
                reportName: WIZARD_PREVIEW_TEMPLATE_NAME,
                baseLocale: 'en_US',
                bodyHtmlTemplate: template,
                query: TextStream.fromString(graphqlFromHtmlGenerator(template, undefined, undefined, localeToUse)),
                styleSheet: TextStream.fromString(await getDocumentEditorStyleSheet()),
                variables: parameters,
                locale: localeToUse,
                paperFormat: paperFormatToUse,
                isExpertDocument: false,
                footerHtmlTemplate: reportSettings.isDefaultHeaderFooter ? defaultHtmlFooter() : '',
                headerHtmlTemplate: reportSettings.isDefaultHeaderFooter
                    ? defaultHtmlHeader(reportSettings.reportName || '')
                    : '',
            });

        const reportObject = await xtremReporting.functions.getReportObjectFromName(
            context,
            WIZARD_PREVIEW_TEMPLATE_NAME,
            [],
        );

        return BinaryStream.fromBuffer(
            await xtremReporting.functions.generatePdfData({
                context,
                reportObject,
                reportName: WIZARD_PREVIEW_TEMPLATE_NAME,
                populatedBodyContent,
                paperFormat: paperFormatToUse,
                pageOrientation: reportSettings.pageOrientation || 'portrait',
                leftMarginCm: reportSettings.leftMarginCm || 2,
                rightMarginCm: reportSettings.rightMarginCm || 2,
                topMarginCm: reportSettings.topMarginCm || 2,
                bottomMarginCm: reportSettings.bottomMarginCm || 2,
                populatedFooterContent,
                populatedHeaderContent,
                isSample: true,
            }),
        );
    }
}

export interface ReportTemplatePreview {
    _id?: string;
    reportName: string;
    isExpertDocument?: boolean;
    baseLocale?: xtremReporting.enums.ReportLocale;
    styleSheet: TextStream;
    externalHtmlTemplate: TextStream;
    externalHeaderHtmlTemplate?: TextStream;
    externalFooterHtmlTemplate?: TextStream;
    query: TextStream;
    code: TextStream;
    translatableTexts?: xtremReporting.functions.TranslatableText[];
    attachmentTemplate: TextStream;
    attachmentName: string;
    attachmentMimeType: string;
    leftMarginCm?: number;
    rightMarginCm?: number;
    topMarginCm?: number;
    bottomMarginCm?: number;
}
